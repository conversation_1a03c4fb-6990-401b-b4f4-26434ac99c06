{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"id": "4ic34NTqEDKp"}, "outputs": [], "source": ["def environment():\n", "  !pip install statsmodels # install the statsmodels library\n", "  !pip install itertools\n", "  !pip install xgboost\n", "  !pip install prophet"]}, {"cell_type": "code", "source": ["  import pandas as pd\n", "  import numpy as np\n", "  import matplotlib.pyplot as plt\n", "  from statsmodels.tsa.holtwinters import ExponentialSmoothing\n", "  from statsmodels.tsa.statespace.sarimax import SARIMAX\n", "  #from xgboost import XGBRegressor\n", "  from typing_extensions import dataclass_transform\n", "  import itertools\n", "  from sklearn.ensemble import RandomForestRegressor\n", "  from xgboost import XGBRegressor\n", "  from sklearn.metrics import mean_squared_error\n", "  from sklearn.model_selection import TimeSeriesSplit, GridSearchCV\n", "  from prophet import Prophet\n", "  from math import e\n", "  from google.colab import drive"], "metadata": {"id": "73JMsKY3dTHV"}, "execution_count": 2, "outputs": []}, {"cell_type": "code", "source": ["def start():\n", "  drive.mount('/content/drive')\n", "  # Voting ensemble helper function\n"], "metadata": {"id": "7jE2_vvLkyrg"}, "execution_count": 3, "outputs": []}, {"cell_type": "code", "source": ["start()"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "QaiUGQl8mewV", "outputId": "9cf48bad-3579-410a-fd2c-ef66496f8737"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Mounted at /content/drive\n"]}]}, {"cell_type": "code", "source": ["# Voting ensemble helper function\n", "def ensemble_predict(predictions):\n", "    return np.mean(predictions, axis=0)\n"], "metadata": {"id": "M8Ka7H9umV8e"}, "execution_count": 5, "outputs": []}, {"cell_type": "code", "source": ["# data = pd.read_csv(\"/content/drive/MyDrive/Colab Notebooks/sales forcast/ATLAS sales forcasting/PF0033301_data.csv\")\n", "data = pd.read_csv('/content/drive/MyDrive/Colab Notebooks/sales forcast/ATLAS sales forcasting/*********_data.csv')"], "metadata": {"id": "UY40tZWilSAu"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "jqMhOvgNT-ds"}, "execution_count": 6, "outputs": []}, {"cell_type": "code", "source": ["def clean_data(data):\n", "  data.info()\n", "  data['ds'] = pd.to_datetime(data['ds'])\n", "  data['ds'].tail()\n", "  y=data['y'].name\n", "  return data,y\n"], "metadata": {"id": "hetzxihtTcgz"}, "execution_count": 7, "outputs": []}, {"cell_type": "code", "source": ["\n", "# data.info()\n"], "metadata": {"id": "g30qGTGHhJQF"}, "execution_count": 8, "outputs": []}, {"cell_type": "code", "source": ["# data['ds'] = pd.to_datetime(data['ds'])"], "metadata": {"id": "zL62qlbJhMzf"}, "execution_count": 9, "outputs": []}, {"cell_type": "code", "source": ["# data['ds'].tail()"], "metadata": {"id": "t5wkGJHX9iw8"}, "execution_count": 10, "outputs": []}, {"cell_type": "code", "source": ["# y=data['y'].name"], "metadata": {"id": "CC7v80yhgnqj"}, "execution_count": 11, "outputs": []}, {"cell_type": "code", "source": ["prediction_data = pd.DataFrame({\n", "    'ds': ['2024-04-01', '2024-05-01','2024-06-01'],\n", "    'MRP': [280,280,280],\n", "    'Discount value': [856480, 252650,856480]\n", "})"], "metadata": {"id": "KzKito-wuYKq"}, "execution_count": 12, "outputs": []}, {"cell_type": "code", "source": ["prediction_data['ds']=pd.to_datetime(prediction_data['ds'])"], "metadata": {"id": "D88Ydzjd9xDK"}, "execution_count": 13, "outputs": []}, {"cell_type": "code", "source": ["data.info()"], "metadata": {"id": "lcjnUVr_h66R", "colab": {"base_uri": "https://localhost:8080/"}, "outputId": "1a5b6b38-4e26-42ef-bda5-bf39b93a54ea"}, "execution_count": 14, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 84 entries, 0 to 83\n", "Data columns (total 6 columns):\n", " #   Column           Non-Null Count  Dtype  \n", "---  ------           --------------  -----  \n", " 0   SKU Code         84 non-null     object \n", " 1   ds               84 non-null     object \n", " 2   MRP              84 non-null     float64\n", " 3   Discount value   84 non-null     float64\n", " 4   SKU Description  84 non-null     object \n", " 5   y                84 non-null     float64\n", "dtypes: float64(3), object(3)\n", "memory usage: 4.1+ KB\n"]}]}, {"cell_type": "code", "source": ["def data_identifying(data,y):\n", "  data.columns = data.columns.str.strip()\n", "  # Identify the date column\n", "  date_column = data.select_dtypes(include=['datetime', 'datetime64']).columns[0]\n", "  # Assuming there's only one date column\n", "\n", "  # Identify the numerical feature columns\n", "  feature_columns = data.select_dtypes(include=['float64', 'int64']).columns.tolist()\n", "\n", "  characteristic_columns = data.select_dtypes(include=['object']).columns.tolist()\n", "  if characteristic_columns!=[]:\n", "    print(f'Error: {characteristic_columns} will not consider as they are not numerical')\n", "\n", "  # Identify the target column\n", "  target_column = y\n", "\n", "  # Remove the target column from the feature list if present\n", "  if target_column in feature_columns:\n", "      feature_columns.remove(target_column)\n", "\n", "  print(\"Date column:\", date_column)\n", "  print(\"Feature columns:\", feature_columns)\n", "  print(\"Target column:\", target_column)\n", "\n", "  plt.figure(figsize=(16, 6))\n", "  plt.plot(data['ds'], data['y'], label='Train Data')\n", "  plt.xlabel('Date')\n", "  plt.ylabel('Sales')\n", "  plt.title('Data overview')\n", "  plt.legend()\n", "\n", "  plt.xticks(rotation=45)\n", "  plt.show()\n", "\n", "  return date_column,feature_columns,target_column"], "metadata": {"id": "nwx9aNBwlHMQ"}, "execution_count": 15, "outputs": []}, {"cell_type": "code", "source": ["def split_data(data):\n", "  #assuming prediction is for 2 data points\n", "  train_data = data[[date_column]+feature_columns+[target_column]][:-3]\n", "  test_data = data[[date_column]+feature_columns+[target_column]][-3:]\n", "  return train_data,test_data\n"], "metadata": {"id": "cLjKBpD5K5LP"}, "execution_count": 16, "outputs": []}, {"cell_type": "code", "source": ["def model_training(train_data,test_data,data,date_column,target_column,feature_columns):\n", "  # Exponential Smoothing with Hyperparameter Tuning\n", "  best_mse = float(\"inf\")\n", "  best_model = None\n", "  for trend in ['add', 'mul']:\n", "      for seasonal in ['add', 'mul']:\n", "          for seasonal_periods in [6, 12]:\n", "              model = ExponentialSmoothing(train_data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()\n", "              pred = model.forecast(len(test_data))\n", "              mse = mean_squared_error(test_data[target_column], pred)\n", "              if mse < best_mse:\n", "                  best_mse = mse\n", "                  best_model = model\n", "\n", "  es_model = best_model\n", "  es_pred = es_model.forecast(len(test_data))\n", "  # SARIMA with Hyperparameter Tuning\n", "  p = d = q = range(0, 2)\n", "  pdq = list(itertools.product(p, d, q))\n", "  seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]\n", "  param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}\n", "  sarima_model = SARIMAX(train_data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})\n", "  sarima_results = sarima_model.fit()\n", "  sarima_pred = sarima_results.forecast(len(test_data))\n", "\n", "  # XGBoost with Hyperparameter Tuning\n", "  param_grid = {\n", "      'n_estimators': [100, 200, 300],\n", "      'learning_rate': [0.1, 0.2, 0.3],\n", "  }\n", "  xgb_model = XGBRegressor()\n", "  grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')\n", "  grid_search.fit(train_data[feature_columns], train_data[[target_column]])\n", "  xgb_model = grid_search.best_estimator_\n", "  xgb_pred = xgb_model.predict(test_data[feature_columns])\n", "\n", "  # Prophet model with regressors\n", "\n", "  # Prepare the data for Prophet\n", "  prophet_data = train_data[[date_column, target_column]].rename(columns={date_column: 'ds', target_column: 'y'})\n", "\n", "  prophet_model = Prophet()\n", "  for regressor in feature_columns:\n", "      prophet_model.add_regressor(regressor)\n", "  prophet_model.fit(train_data)\n", "\n", "  # Predict future dates\n", "  future = test_data[['ds']].copy()  #prediction for 2 months\n", "  for regressor in feature_columns:\n", "      future[regressor] = test_data[regressor]  # Align feature values\n", "\n", "  forecast = prophet_model.predict(future)\n", "  prophet_pred = forecast['yhat'][-len(test_data):]\n", "\n", "    # Calculate RMSE for each model\n", "  es_rmse = np.sqrt(mean_squared_error(test_data[target_column], es_pred))\n", "  sarima_rmse = np.sqrt(mean_squared_error(test_data[target_column], sarima_pred))\n", "  xgb_rmse = np.sqrt(mean_squared_error(test_data[target_column], xgb_pred))\n", "  prophet_rmse = np.sqrt(mean_squared_error(test_data[target_column], prophet_pred))\n", "\n", "   # Calculate accuracy for each model\n", "  es_accuracy = 100 - (es_rmse / test_data[target_column].mean()) * 100\n", "  sarima_accuracy = 100 - (sarima_rmse / test_data[target_column].mean()) * 100\n", "  xgb_accuracy = 100 - (xgb_rmse / test_data[target_column].mean()) * 100\n", "  prophet_accuracy = 100 - (prophet_rmse / test_data[target_column].mean()) * 100\n", "\n", "  # Print the results\n", "  print(f'Exponential Smoothing RMSE: {es_rmse:.2f}')\n", "  print(f'Exponential Smoothing Accuracy: {es_accuracy:.2f}%')\n", "\n", "  print(f'SARIMA RMSE: {sarima_rmse:.2f}')\n", "  print(f'SARIMA Accuracy: {sarima_accuracy:.2f}%')\n", "\n", "  print(f'XGBoost RMSE: {xgb_rmse:.2f}')\n", "  print(f'XGBoost Accuracy: {xgb_accuracy:.2f}%')\n", "\n", "\n", "  print(f'Prophet RMSE: {prophet_rmse:.2f}')\n", "  print(f'Prophet Accuracy: {prophet_accuracy:.2f}%')\n", "\n", "  # Combine predictions from models with accuracy > 50%\n", "  predictions = []\n", "  if es_accuracy > 50:\n", "    predictions.append(es_pred)\n", "  if sarima_accuracy > 50:\n", "    predictions.append(sarima_pred)\n", "  if xgb_accuracy > 50:\n", "    predictions.append(xgb_pred)\n", "  if prophet_accuracy > 50:\n", "    predictions.append(prophet_pred)\n", "  # Calculate ensemble prediction\n", "  if predictions:\n", "    ensemble_pred = ensemble_predict(np.array(predictions))\n", "    ensemble_rmse = np.sqrt(mean_squared_error(test_data[target_column], ensemble_pred))\n", "    ensemble_accuracy = 100 - (np.mean(np.abs((test_data[target_column] - ensemble_pred) / test_data[target_column])) * 100)\n", "    print(f'Ensemble RMSE: {ensemble_rmse:.2f}')\n", "    print(f'Ensemble Accuracy: {ensemble_accuracy:.2f}%')\n", "  else:\n", "    print(\"No models with accuracy greater than 50% to create an ensemble.\")\n", "\n", "  # Calculate ensemble prediction\n", "  if predictions:\n", "    ensemble_pred = ensemble_predict(np.array(predictions))\n", "    #check the best accurate model\n", "  if es_accuracy > sarima_accuracy and es_accuracy > xgb_accuracy and es_accuracy > prophet_accuracy and es_accuracy >= ensemble_accuracy :\n", "    print(f'Exponential Smoothing has the highest accuracy: {es_accuracy:.2f}%')\n", "    print(es_pred)\n", "    best_model = es_model\n", "    best_accuracy = es_accuracy\n", "    best_prediction = es_pred\n", "    best_model_name = 'Exponential Smoothing'\n", "  elif sarima_accuracy > es_accuracy and sarima_accuracy > xgb_accuracy and sarima_accuracy > prophet_accuracy and sarima_accuracy >= ensemble_accuracy:\n", "    print(f'SARIMA has the highest accuracy: {sarima_accuracy:.2f}%')\n", "    print(sarima_pred)\n", "    best_model = sarima_results\n", "    best_accuracy = sarima_accuracy\n", "    best_prediction = sarima_pred\n", "    best_model_name = 'SARIMA'\n", "  elif xgb_accuracy > es_accuracy and xgb_accuracy > sarima_accuracy and xgb_accuracy > prophet_accuracy and xgb_accuracy >= ensemble_accuracy:\n", "    print(f'XGBoost has the highest accuracy: {xgb_accuracy:.2f}%')\n", "    print(xgb_pred)\n", "    best_model = xgb_model\n", "    best_accuracy = xgb_accuracy\n", "    best_prediction = xgb_pred\n", "    best_model_name = 'XGBoost'\n", "  elif prophet_accuracy > es_accuracy and prophet_accuracy > sarima_accuracy and prophet_accuracy > xgb_accuracy and prophet_accuracy >= ensemble_accuracy:\n", "    print(f'Prophet has the highest accuracy: {prophet_accuracy:.2f}%')\n", "    print(prophet_pred)\n", "    best_model = prophet_model\n", "    best_accuracy = prophet_accuracy\n", "    best_prediction = prophet_pred\n", "    best_model_name = '<PERSON>'\n", "  elif ensemble_accuracy > es_accuracy and ensemble_accuracy > sarima_accuracy and ensemble_accuracy > xgb_accuracy and ensemble_accuracy > prophet_accuracy:\n", "    print(f'Ensemble Model has the highest accuracy: {ensemble_accuracy:.2f}%')\n", "    print(ensemble_pred)\n", "    best_model = ensemble_pred\n", "    best_accuracy = ensemble_accuracy\n", "    best_prediction = ensemble_pred\n", "    best_model_name = 'ensemble'\n", "  else:\n", "    print('The accuracy is lower than50% please provide more data')\n", "  # Plot the results\n", "  plt.figure(figsize=(10, 6))\n", "  plt.plot(test_data[date_column], test_data[target_column], label='Actual')\n", "  plt.plot(test_data[date_column], ensemble_pred, label='Ensemble Prediction')\n", "  plt.plot(test_data[date_column], xgb_pred, label='XGBoost')\n", "  plt.plot(test_data[date_column], sarima_pred, label='SARIMA')\n", "  plt.plot(test_data[date_column], es_pred, label='Exponential Smoothing')\n", "  plt.plot(test_data[date_column], prophet_pred, label='Prophet')\n", "  plt.xlabel('Date')\n", "  plt.ylabel('Sales')\n", "  plt.title('Prediction vs Actual Sales')\n", "  plt.legend()\n", "  plt.xticks(rotation=45)\n", "  plt.show()\n", "\n", "  return best_model,best_accuracy,best_prediction,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy,best_model_name\n"], "metadata": {"id": "EuS00yclqY3H"}, "execution_count": 17, "outputs": []}, {"cell_type": "code", "source": ["# def best_model_training(data,best_model_name,feature_columns_pred,date_column_pred,feature_columns,date_column,target_column,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy):\n", "#    # Exponential Smoothing with Hyperparameter Tuning\n", "#     if best_model_name == 'Exponential Smoothing':\n", "#       best_mse = float(\"inf\")\n", "#       best_model = None\n", "#       for trend in ['add', 'mul']:\n", "#           for seasonal in ['add', 'mul']:\n", "#               for seasonal_periods in [6, 12]:\n", "#                   model = ExponentialSmoothing(data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()\n", "#                   es_pred = model.forecast(2)\n", "#                   return es_pred\n", "\n", "#     elif best_model_name == 'SARIMA':\n", "#       p = d = q = range(0, 2)\n", "#       pdq = list(itertools.product(p, d, q))\n", "#       seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]\n", "#       param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}\n", "#       sarima_model = SARIMAX(data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})\n", "#       sarima_results = sarima_model.fit()\n", "#       sarima_pred = sarima_results.forecast(2)\n", "#       return sarima_pred\n", "\n", "#     elif best_model_name == 'Xgboost':\n", "#       param_grid = {\n", "#           'n_estimators': [100, 200, 300],\n", "#           'learning_rate': [0.1, 0.2, 0.3],\n", "#       }\n", "#       xgb_model = XGBRegressor()\n", "#       grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')\n", "#       grid_search.fit(data[feature_columns], data[[target_column]])\n", "#       xgb_model = grid_search.best_estimator_\n", "#       xgb_pred = xgb_model.predict(feature_columns_pred)\n", "#       return xgb_pred\n", "\n", "#     elif best_model_name == 'Prophet':\n", "#       #Prepare the data for Prophet\n", "#       prophet_data = data[[date_column, target_column]].rename(columns={date_column: 'ds', target_column: 'y'})\n", "#       prophet_model = Prophet()\n", "#       for regressor in feature_columns:\n", "#           prophet_model.add_regressor(regressor)\n", "#       prophet_model.fit(data)\n", "#       # Predict future dates\n", "#       future = test_data[['ds']].copy() #prediction for 2 months\n", "#       for regressor in feature_columns:\n", "#           future[regressor] = test_data[regressor]  # Align feature values\n", "\n", "#       forecast = prophet_model.predict(future)\n", "#       prophet_pred = forecast['yhat'][-2:]\n", "#       return prophet_pred\n", "#     else:\n", "#     # Calculate RMSE for each model\n", "\n", "#   # Print the results\n", "#   # Combine predictions from models with accuracy > 50%\n", "#       predictions = []\n", "#       if es_accuracy > 50:\n", "#         predictions.append(es_pred)\n", "#       if sarima_accuracy > 50:\n", "#         predictions.append(sarima_pred)\n", "#       if xgb_accuracy > 50:\n", "#         predictions.append(xgb_pred)\n", "#       if prophet_accuracy > 50:\n", "#         predictions.append(prophet_pred)\n", "#       # Calculate ensemble prediction\n", "#       if predictions:\n", "#         ensemble_pred = ensemble_predict(np.array(predictions))\n", "#         ensemble_rmse = np.sqrt(mean_squared_error(test_data[target_column], ensemble_pred))\n", "#         ensemble_accuracy = 100 - (np.mean(np.abs((test_data[target_column] - ensemble_pred) / test_data[target_column])) * 100)\n", "#         print(f'Ensemble RMSE: {ensemble_rmse:.2f}')\n", "#         print(f'Ensemble Accuracy: {ensemble_accuracy:.2f}%')\n", "#       else:\n", "#         print(\"No models with accuracy greater than 50% to create an ensemble.\")\n", "\n", "#       # Calculate ensemble prediction\n", "#       if predictions:\n", "#         ensemble_pred = ensemble_predict(np.array(predictions))\n", "      ###########################################################\n", "\n"], "metadata": {"id": "Pg0-c3GHt_pA"}, "execution_count": 18, "outputs": []}, {"cell_type": "code", "source": ["def best_model_training(data,best_model_name,feature_pred,prediction_data,feature_columns,date_column,target_column,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy):\n", "      if best_model_name == 'Exponential Smoothing':\n", "        best_mse = float(\"inf\")\n", "        best_model = None\n", "        for trend in ['add', 'mul']:\n", "            for seasonal in ['add', 'mul']:\n", "                for seasonal_periods in [6, 12]:\n", "                    model = ExponentialSmoothing(data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()\n", "                    es_pred = model.forecast(3)\n", "        return es_pred\n", "        print (f'exponential smoothing output{es_pred}')\n", "\n", "      elif best_model_name == 'SARIMA':\n", "        p = d = q = range(0, 2)\n", "        pdq = list(itertools.product(p, d, q))\n", "        seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]\n", "        param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}\n", "        sarima_model = SARIMAX(data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})\n", "        sarima_results = sarima_model.fit()\n", "        sarima_pred = sarima_results.forecast(3)\n", "        return sarima_pred\n", "        print (f'sarima output{sarima_pred}')\n", "\n", "      elif best_model_name == 'XGBoost':\n", "          param_grid = {\n", "          'n_estimators': [100, 200, 300],\n", "          'learning_rate': [0.1, 0.2, 0.3],\n", "            }\n", "          xgb_model = XGBRegressor()\n", "          grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')\n", "          grid_search.fit(data[feature_columns], data[[target_column]])\n", "          xgb_model = grid_search.best_estimator_\n", "          xgb_pred = xgb_model.predict(feature_pred)\n", "          return xgb_pred\n", "          print (f'xgb output{xgb_pred}')\n", "\n", "      elif best_model_name == 'Prophet':\n", "        # prophet_data = data[[date_column, target_column]+feature_columns].rename(columns={date_column: 'ds', target_column: 'y'})\n", "        # prophet_data.append(feature_pred)\n", "        # prophet_data = pd.concat([prophet_data, feature_pred], axis=0)\n", "        prophet_model = Prophet()\n", "        for regressor in feature_columns:\n", "            prophet_model.add_regressor(regressor)\n", "        prophet_model.fit(data)\n", "        # Predict future dates\n", "        future = prediction_data[['ds']].copy()#prediction for 2 months\n", "        for regressor in feature_columns:\n", "            future[regressor] = feature_pred[regressor]  # Align feature values\n", "        forecast = prophet_model.predict(future)\n", "        prophet_pred = forecast['yhat'][-3:]\n", "        return prophet_pred\n", "        print (f'prophet output{prophet_pred}')\n", "\n", "      else:\n", "        best_mse = float(\"inf\")\n", "        best_model = None\n", "        for trend in ['add', 'mul']:\n", "            for seasonal in ['add', 'mul']:\n", "                for seasonal_periods in [6, 12]:\n", "                    model = ExponentialSmoothing(data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()\n", "                    es_pred = model.forecast(3)\n", "\n", "        p = d = q = range(0, 2)\n", "        pdq = list(itertools.product(p, d, q))\n", "        seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]\n", "        param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}\n", "        sarima_model = SARIMAX(data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})\n", "        sarima_results = sarima_model.fit()\n", "        sarima_pred = sarima_results.forecast(3)\n", "\n", "        param_grid = {\n", "            'n_estimators': [100, 200, 300],\n", "            'learning_rate': [0.1, 0.2, 0.3],\n", "        }\n", "        xgb_model = XGBRegressor()\n", "        grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')\n", "        grid_search.fit(data[feature_columns], data[[target_column]])\n", "        xgb_model = grid_search.best_estimator_\n", "        xgb_pred = xgb_model.predict(feature_pred)\n", "\n", "        prophet_model = Prophet()\n", "        for regressor in feature_columns:\n", "            prophet_model.add_regressor(regressor)\n", "        prophet_model.fit(data)\n", "        # Predict future dates\n", "        future = prediction_data[['ds']].copy()#prediction for 2 months\n", "        for regressor in feature_columns:\n", "            future[regressor] = feature_pred[regressor]  # Align feature values\n", "        forecast = prophet_model.predict(future)\n", "        prophet_pred = forecast['yhat'][-3:]\n", "        return prophet_pred\n", "        print (f'prophet output{prophet_pred}')\n", "\n", "\n", "\n", "        predictions = []\n", "        if es_accuracy > 50:\n", "            predictions.append(es_pred)\n", "        if sarima_accuracy > 50:\n", "            predictions.append(sarima_pred)\n", "        if xgb_accuracy > 50:\n", "            predictions.append(xgb_pred)\n", "        if prophet_accuracy > 50:\n", "            predictions.append(prophet_pred)\n", "        # Calculate ensemble prediction\n", "        if predictions:\n", "            ensemble_pred = ensemble_predict(np.array(predictions))\n", "            return ensemble_pred\n", "            # ensemble_rmse = np.sqrt(mean_squared_error(test_data[target_column], ensemble_pred))\n", "            # ensemble_accuracy = 100 - (np.mean(np.abs((test_data[target_column] - ensemble_pred) / test_data[target_column])) * 100)\n", "            # print(f'Ensemble RMSE: {ensemble_rmse:.2f}')\n", "            # print(f'Ensemble Accuracy: {ensemble_accuracy:.2f}%')\n", "        else:\n", "            print(\"No models with accuracy greater than 50% to create an ensemble.\")\n"], "metadata": {"id": "DnE7TlE47Xsq"}, "execution_count": 19, "outputs": []}, {"cell_type": "code", "source": ["data.head()"], "metadata": {"id": "3LANixboebFM", "colab": {"base_uri": "https://localhost:8080/", "height": 206}, "outputId": "a41f8015-d216-44ad-ee8c-867b76aad983"}, "execution_count": 20, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["    SKU Code          ds    MRP  Discount value             SKU Description  \\\n", "0  *********  2017-04-01  125.0             0.0  Atl Book B5 (=) 160Pg-0040   \n", "1  *********  2017-05-01  125.0             0.0  Atl Book B5 (=) 160Pg-0040   \n", "2  *********  2017-06-01  125.0             0.0  Atl Book B5 (=) 160Pg-0040   \n", "3  *********  2017-07-01  125.0             0.0  Atl Book B5 (=) 160Pg-0040   \n", "4  *********  2017-08-01  125.0             0.0  Atl Book B5 (=) 160Pg-0040   \n", "\n", "        y  \n", "0   666.0  \n", "1  1154.0  \n", "2  1274.0  \n", "3   837.0  \n", "4   924.0  "], "text/html": ["\n", "  <div id=\"df-e0a7aa80-c90a-4145-9a25-9321323faf14\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>SKU Code</th>\n", "      <th>ds</th>\n", "      <th>MRP</th>\n", "      <th>Discount value</th>\n", "      <th>SKU Description</th>\n", "      <th>y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>*********</td>\n", "      <td>2017-04-01</td>\n", "      <td>125.0</td>\n", "      <td>0.0</td>\n", "      <td>Atl Book B5 (=) 160Pg-0040</td>\n", "      <td>666.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>*********</td>\n", "      <td>2017-05-01</td>\n", "      <td>125.0</td>\n", "      <td>0.0</td>\n", "      <td>Atl Book B5 (=) 160Pg-0040</td>\n", "      <td>1154.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>*********</td>\n", "      <td>2017-06-01</td>\n", "      <td>125.0</td>\n", "      <td>0.0</td>\n", "      <td>Atl Book B5 (=) 160Pg-0040</td>\n", "      <td>1274.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>*********</td>\n", "      <td>2017-07-01</td>\n", "      <td>125.0</td>\n", "      <td>0.0</td>\n", "      <td>Atl Book B5 (=) 160Pg-0040</td>\n", "      <td>837.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>*********</td>\n", "      <td>2017-08-01</td>\n", "      <td>125.0</td>\n", "      <td>0.0</td>\n", "      <td>Atl Book B5 (=) 160Pg-0040</td>\n", "      <td>924.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-e0a7aa80-c90a-4145-9a25-9321323faf14')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-e0a7aa80-c90a-4145-9a25-9321323faf14 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-e0a7aa80-c90a-4145-9a25-9321323faf14');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-a3a4f416-a72f-4015-97b2-cde68f67627b\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-a3a4f416-a72f-4015-97b2-cde68f67627b')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-a3a4f416-a72f-4015-97b2-cde68f67627b button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "\n", "    </div>\n", "  </div>\n"], "application/vnd.google.colaboratory.intrinsic+json": {"type": "dataframe", "variable_name": "data", "summary": "{\n  \"name\": \"data\",\n  \"rows\": 84,\n  \"fields\": [\n    {\n      \"column\": \"SKU Code\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"*********\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"ds\",\n      \"properties\": {\n        \"dtype\": \"object\",\n        \"num_unique_values\": 84,\n        \"samples\": [\n          \"2023-05-01\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"MRP\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 81.14576371225553,\n        \"min\": 125.0,\n        \"max\": 360.0,\n        \"num_unique_values\": 9,\n        \"samples\": [\n          310.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"Discount value\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 222405.8904010057,\n        \"min\": -1142.0,\n        \"max\": 1322875.0,\n        \"num_unique_values\": 44,\n        \"samples\": [\n          42949.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"SKU Description\",\n      \"properties\": {\n        \"dtype\": \"category\",\n        \"num_unique_values\": 1,\n        \"samples\": [\n          \"Atl Book B5 (=) 160Pg-0040\"\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    },\n    {\n      \"column\": \"y\",\n      \"properties\": {\n        \"dtype\": \"number\",\n        \"std\": 25219.374862508164,\n        \"min\": 45.0,\n        \"max\": 113350.0,\n        \"num_unique_values\": 84,\n        \"samples\": [\n          23459.0\n        ],\n        \"semantic_type\": \"\",\n        \"description\": \"\"\n      }\n    }\n  ]\n}"}}, "metadata": {}, "execution_count": 20}]}, {"cell_type": "code", "source": ["def future_prediction(prediction_data,data,date_column,feature_columns,target_column,best_model_name,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy):\n", "    feature_columns_pred = prediction_data.select_dtypes(include=['float64', 'int64']).columns.tolist()\n", "    date_column_pred = prediction_data.select_dtypes(include=['datetime', 'datetime64']).columns[0]\n", "    prediction_data[date_column_pred].rename('ds', inplace=True)\n", "    # prediction_data[target_column].rename('y', inplace=True)\n", "    data = data[[date_column, target_column]+feature_columns].rename(columns={date_column: 'ds', target_column: 'y'})\n", "    # print(data.head())\n", "    # print(prediction_data.head())\n", "    # print(feature_columns_pred)\n", "  # Validate the columns before proceeding to prediction\n", "    try:\n", "        check_column_alignment(feature_columns, feature_columns_pred)\n", "    except ValueError as e:\n", "        print(e)\n", "    else:\n", "      date_pred=prediction_data[date_column_pred]\n", "      feature_pred=prediction_data[feature_columns_pred]\n", "      print(date_pred)\n", "      print(feature_pred)\n", "\n", "      pred=best_model_training(data,best_model_name,feature_pred,prediction_data,feature_columns,date_column,target_column,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy)\n", "\n", "      # if best_model_name == 'Exponential Smoothing':\n", "      #   future_pred = best_model.forecast(len(prediction_data)+2).tail(2)\n", "      # elif best_model_name == 'SARIMA':\n", "      #   future_pred = best_model.forecast(len(prediction_data)+2).tail(2)\n", "      # elif best_model_name == 'XGBoost':\n", "      #   # feature_columns_concat= pd.concat([test_data[feature_columns],feature_columns_pred])\n", "      #   # future_pred = best_model.predict(feature_columns_concat).tail(2)\n", "      #   pred=best_model_training(data,best_model_name,feature_columns_pred,date_column_pred,feature_columns,date_column,target_column)\n", "      #   # future_pred = best_model(data,best_model_name,feature_columns_pred,feature_columns,date_column,target_column)\n", "      #   # future_pred = pd.Series(future_pred)\n", "      # elif best_model_name == 'Prophet':\n", "      #   feature_columns_concat=pd.concat([test_data[feature_columns],feature_columns_pred])\n", "      #   future_pred = best_model.predict(feature_columns_concat).tail(2)\n", "      # else :\n", "      #   print(\"THERE WAS AN ERROR FIX IT HERE ENSEMBLE MODEL SHOULD RUN\")\n", "\n", "      return pred\n", "\n"], "metadata": {"id": "njRLB3vWt0gS"}, "execution_count": 21, "outputs": []}, {"cell_type": "code", "source": ["def check_column_alignment(train_columns, prediction_columns):\n", "      missing_columns = set(train_columns) - set(prediction_columns)\n", "      extra_columns = set(prediction_columns) - set(train_columns)\n", "      error_message = \"\"\n", "      if missing_columns or extra_columns:\n", "          # error_message = \"\"\n", "          if missing_columns:\n", "              error_message += f\"Missing columns in prediction data: {missing_columns}\\n\"\n", "          if extra_columns:\n", "              error_message += f\"Extra columns in prediction data: {extra_columns}\\n\"\n", "      if error_message:\n", "        raise ValueError(f\"Column mismatch error:\\n{error_message}\")\n", "      else:\n", "       None"], "metadata": {"id": "McETlwU9wX5H"}, "execution_count": 22, "outputs": []}, {"cell_type": "code", "source": ["clean_data= clean_data(data)\n", "data=clean_data[0]\n", "y=clean_data[1]\n", "cleaned_data=data_identifying(data,y)\n", "date_column=cleaned_data[0]\n", "feature_columns=cleaned_data[1]\n", "target_column=cleaned_data[2]\n", "\n", "data_splitted=split_data(data)\n", "train_data=data_splitted[0]\n", "test_data=data_splitted[1]\n", "\n", "model_selected=model_training(train_data,test_data,data,date_column,target_column,feature_columns)\n", "best_model=model_selected[0]\n", "best_accuracy=model_selected[1]\n", "best_prediction=model_selected[2]\n", "xgb_accuracy=model_selected[3]\n", "es_accuracy=model_selected[4]\n", "sarima_accuracy=model_selected[5]\n", "prophet_accuracy=model_selected[6]\n", "best_model_name=model_selected[7]\n", "# best_model_name='XGBoost'\n", "prediction = future_prediction(prediction_data,data,date_column,feature_columns,target_column,best_model_name,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy)\n", "print(prediction)\n", "# best_model=model_selected[0]\n", "# best_accuracy=model_selected[1]\n", "# best_prediction=model_selected[2]\n", "# xgb_accuracy=model_selected[3]\n", "# es_accuracy=model_selected[4]\n", "# sarima_accuracy=model_selected[5]\n", "# prophet_accuracy=model_selected[6]\n", "# xgb_pred=model_selected[7]\n", "# es_pred=model_selected[8]\n", "# sarima_pred=model_selected[9]\n", "# prophet_pred=model_selected[10]\n", "# best_model_name=model_selected[11]\n", "\n", "\n", "# print(f'best model{best_model_name}')\n", "# print(best_accuracy)\n", "# print(best_prediction)\n", "# # print(f'ensemble prediction{prediction_ensemble}')\n", "# if best_model_name != 'ensemble':\n", "#   print(f'best prediction{best_prediction}')\n", "#   # prediction=future_prediction(prediction_data,date_column,feature_columns,target_column,best_model,best_model_name)\n", "#   prediction = best_model\n", "#   print(f'finalprediction = {prediction}')\n", "# else:\n", "#     predictions = []\n", "#     if es_accuracy > 50:\n", "#        predictions.append(es_pred)\n", "#     if sarima_accuracy > 50:\n", "#       predictions.append(sarima_pred)\n", "#     if xgb_accuracy > 50:\n", "#       predictions.append(xgb_pred)\n", "#     if prophet_accuracy > 50:\n", "#       predictions.append(prophet_pred)\n", "#       if predictions:\n", "#           ensemble_pred = ensemble_predict(np.array(predictions))\n", "#           prediction_ensemble = ensemble_pred\n", "#           print(f'Ensemble prediction: {ensemble_pred}')\n", "#     else:\n", "#           print(\"No models with accuracy greater than 50% to create an ensemble.\")"], "metadata": {"id": "lOKgzanJ4n3n", "colab": {"base_uri": "https://localhost:8080/", "height": 1000}, "outputId": "f91404cb-b7ec-4ef4-a32e-c4dc4f4fe6d3"}, "execution_count": 23, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 84 entries, 0 to 83\n", "Data columns (total 6 columns):\n", " #   Column           Non-Null Count  Dtype  \n", "---  ------           --------------  -----  \n", " 0   SKU Code         84 non-null     object \n", " 1   ds               84 non-null     object \n", " 2   MRP              84 non-null     float64\n", " 3   Discount value   84 non-null     float64\n", " 4   SKU Description  84 non-null     object \n", " 5   y                84 non-null     float64\n", "dtypes: float64(3), object(3)\n", "memory usage: 4.1+ KB\n", "Error: ['SKU Code', 'SKU Description'] will not consider as they are not numerical\n", "Date column: ds\n", "Feature columns: ['MRP', 'Discount value']\n", "Target column: y\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1600x600 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stderr", "text": ["/usr/local/lib/python3.11/dist-packages/statsmodels/tsa/holtwinters/model.py:918: ConvergenceWarning: Optimization failed to converge. Check mle_retvals.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/statsmodels/tsa/holtwinters/model.py:918: ConvergenceWarning: Optimization failed to converge. Check mle_retvals.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/statsmodels/tsa/holtwinters/model.py:918: ConvergenceWarning: Optimization failed to converge. Check mle_retvals.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/statsmodels/tsa/holtwinters/model.py:918: ConvergenceWarning: Optimization failed to converge. Check mle_retvals.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/statsmodels/tsa/holtwinters/model.py:918: ConvergenceWarning: Optimization failed to converge. Check mle_retvals.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/statsmodels/tsa/holtwinters/model.py:918: ConvergenceWarning: Optimization failed to converge. Check mle_retvals.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/statsmodels/tsa/holtwinters/model.py:918: ConvergenceWarning: Optimization failed to converge. Check mle_retvals.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/statsmodels/tsa/holtwinters/model.py:918: ConvergenceWarning: Optimization failed to converge. Check mle_retvals.\n", "  warnings.warn(\n", "/usr/local/lib/python3.11/dist-packages/statsmodels/tsa/statespace/sarimax.py:966: UserWarning: Non-stationary starting autoregressive parameters found. Using zeros as starting parameters.\n", "  warn('Non-stationary starting autoregressive parameters'\n", "/usr/local/lib/python3.11/dist-packages/statsmodels/tsa/statespace/sarimax.py:978: UserWarning: Non-invertible starting MA parameters found. Using zeros as starting parameters.\n", "  warn('Non-invertible starting MA parameters found.'\n", "/usr/local/lib/python3.11/dist-packages/statsmodels/base/model.py:607: ConvergenceWarning: Maximum Likelihood optimization failed to converge. Check mle_retvals\n", "  warnings.warn(\"Maximum Likelihood optimization failed to \"\n", "INFO:prophet:Disabling weekly seasonality. Run prophet with weekly_seasonality=True to override this.\n", "INFO:prophet:Disabling daily seasonality. Run prophet with daily_seasonality=True to override this.\n", "DEBUG:cmdstanpy:input tempfile: /tmp/tmp99qvzm75/0mp0lq_k.json\n", "DEBUG:cmdstanpy:input tempfile: /tmp/tmp99qvzm75/u1hssnz6.json\n", "DEBUG:cmdstanpy:idx 0\n", "DEBUG:cmdstanpy:running CmdStan, num_threads: None\n", "DEBUG:cmdstanpy:CmdStan args: ['/usr/local/lib/python3.11/dist-packages/prophet/stan_model/prophet_model.bin', 'random', 'seed=64622', 'data', 'file=/tmp/tmp99qvzm75/0mp0lq_k.json', 'init=/tmp/tmp99qvzm75/u1hssnz6.json', 'output', 'file=/tmp/tmp99qvzm75/prophet_modelcu1y5pqb/prophet_model-20250409091342.csv', 'method=optimize', 'algorithm=newton', 'iter=10000']\n", "09:13:42 - cmdstanpy - INFO - Chain [1] start processing\n", "INFO:cmdstanpy:Chain [1] start processing\n", "09:13:43 - cmdstanpy - INFO - Chain [1] done processing\n", "INFO:cmdstanpy:Chain [1] done processing\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Exponential Smoothing RMSE: 32562.86\n", "Exponential Smoothing Accuracy: 59.02%\n", "SARIMA RMSE: 27407.49\n", "SARIMA Accuracy: 65.51%\n", "XGBoost RMSE: 16414.65\n", "XGBoost Accuracy: 79.34%\n", "Prophet RMSE: 29515.33\n", "Prophet Accuracy: 62.86%\n", "Ensemble RMSE: 23612.59\n", "Ensemble Accuracy: 74.60%\n", "XGBoost has the highest accuracy: 79.34%\n", "[96525.99   87822.61   11162.6455]\n"]}, {"output_type": "display_data", "data": {"text/plain": ["<Figure size 1000x600 with 1 Axes>"], "image/png": "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\n"}, "metadata": {}}, {"output_type": "stream", "name": "stdout", "text": ["0   2024-04-01\n", "1   2024-05-01\n", "2   2024-06-01\n", "Name: ds, dtype: datetime64[ns]\n", "   MRP  Discount value\n", "0  280          856480\n", "1  280          252650\n", "2  280          856480\n", "[87824.414 51921.83  87824.414]\n"]}]}]}