from sales_forecast.pipeline.data_preparation import clean_data, data_identifying
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import mean_squared_error
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from statsmodels.tsa.statespace.sarimax import SARIMAX
from xgboost import XGBRegressor
from prophet import Prophet
import itertools
import numpy as np
import pandas as pd


# data, date_column, target_column, feature_columns = data_identifying(data,y)

# def split_data(data):
#   #assuming prediction is for 2 data points
#   train_data = data[[date_column]+feature_columns+[target_column]][:-3]
#   test_data = data[[date_column]+feature_columns+[target_column]][-3:]
#   return train_data,test_data

def ensemble_predict(predictions):
    return np.mean(predictions, axis=0)

def model_training(train_data,test_data,data,date_column,target_column,feature_columns):
  # Exponential Smoothing with Hyperparameter Tuning
  best_mse = float("inf")
  best_model = None
  for trend in ['add', 'mul']:
      for seasonal in ['add', 'mul']:
          for seasonal_periods in [6, 12]:
              model = ExponentialSmoothing(train_data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()
              pred = model.forecast(len(test_data))
              mse = mean_squared_error(test_data[target_column], pred)
              if mse < best_mse:
                  best_mse = mse
                  best_model = model

  es_model = best_model
  es_pred = es_model.forecast(len(test_data))
  # SARIMA with Hyperparameter Tuning
  p = d = q = range(0, 2)
  pdq = list(itertools.product(p, d, q))
  seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]
  param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}
  sarima_model = SARIMAX(train_data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})
  sarima_results = sarima_model.fit()
  sarima_pred = sarima_results.forecast(len(test_data))

  # XGBoost with Hyperparameter Tuning
  xgb_pred = None
  xgb_model = None
  xgb_rmse = None
  xgb_accuracy = None
  if feature_columns and len(feature_columns) > 0:
      param_grid = {
          'n_estimators': [100, 200, 300],
          'learning_rate': [0.1, 0.2, 0.3],
      }
      xgb_model = XGBRegressor()
      grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')
      grid_search.fit(train_data[feature_columns], train_data[[target_column]])
      xgb_model = grid_search.best_estimator_
      xgb_pred = xgb_model.predict(test_data[feature_columns])
      xgb_rmse = np.sqrt(mean_squared_error(test_data[target_column], xgb_pred))
      xgb_accuracy = 100 - (xgb_rmse / test_data[target_column].mean()) * 100
  else:
      print("Skipping XGBoost: No feature columns available.")
      xgb_rmse = float('inf')
      xgb_accuracy = 0

  # Prophet model with regressors

  # Prepare the data for Prophet
  prophet_data = train_data[[date_column, target_column]].rename(columns={date_column: 'ds', target_column: 'y'})

  prophet_model = Prophet()
  for regressor in feature_columns:
      prophet_model.add_regressor(regressor)
  prophet_model.fit(train_data)

  # Predict future dates
  future = test_data[['ds']].copy()  #prediction for 2 months
  for regressor in feature_columns:
      future[regressor] = test_data[regressor]  # Align feature values

  forecast = prophet_model.predict(future)
  prophet_pred = forecast['yhat'][-len(test_data):]

    # Calculate RMSE for each model
  es_rmse = np.sqrt(mean_squared_error(test_data[target_column], es_pred))
  sarima_rmse = np.sqrt(mean_squared_error(test_data[target_column], sarima_pred))
  if xgb_pred is not None:
      xgb_rmse = np.sqrt(mean_squared_error(test_data[target_column], xgb_pred))
      xgb_accuracy = 100 - (xgb_rmse / test_data[target_column].mean()) * 100
  else:
      xgb_rmse = float('inf')
      xgb_accuracy = 0
  prophet_rmse = np.sqrt(mean_squared_error(test_data[target_column], prophet_pred))
  prophet_accuracy = 100 - (prophet_rmse / test_data[target_column].mean()) * 100

   # Calculate accuracy for each model
  es_accuracy = 100 - (es_rmse / test_data[target_column].mean()) * 100
  sarima_accuracy = 100 - (sarima_rmse / test_data[target_column].mean()) * 100
  xgb_accuracy = 100 - (xgb_rmse / test_data[target_column].mean()) * 100
  prophet_accuracy = 100 - (prophet_rmse / test_data[target_column].mean()) * 100

  # Print the results
  print(f'Exponential Smoothing RMSE: {es_rmse:.2f}')
  print(f'Exponential Smoothing Accuracy: {es_accuracy:.2f}%')

  print(f'SARIMA RMSE: {sarima_rmse:.2f}')
  print(f'SARIMA Accuracy: {sarima_accuracy:.2f}%')

  print(f'XGBoost RMSE: {xgb_rmse:.2f}')
  print(f'XGBoost Accuracy: {xgb_accuracy:.2f}%')


  print(f'Prophet RMSE: {prophet_rmse:.2f}')
  print(f'Prophet Accuracy: {prophet_accuracy:.2f}%')

  # Combine predictions from models with accuracy > 50%
  predictions = []
  if es_accuracy > 50:
    predictions.append(es_pred)
  if sarima_accuracy > 50:
    predictions.append(sarima_pred)
  if xgb_accuracy > 50:
    predictions.append(xgb_pred)
  if prophet_accuracy > 50:
    predictions.append(prophet_pred)
  # Calculate ensemble prediction
  if predictions:
    ensemble_pred = ensemble_predict(np.array(predictions))
    ensemble_rmse = np.sqrt(mean_squared_error(test_data[target_column], ensemble_pred))
    ensemble_accuracy = 100 - (np.mean(np.abs((test_data[target_column] - ensemble_pred) / test_data[target_column])) * 100)
    print(f'Ensemble RMSE: {ensemble_rmse:.2f}')
    print(f'Ensemble Accuracy: {ensemble_accuracy:.2f}%')
  else:
    print("No models with accuracy greater than 50% to create an ensemble.")

  # Calculate ensemble prediction
  if predictions:
    ensemble_pred = ensemble_predict(np.array(predictions))
    #check the best accurate model
  if es_accuracy > sarima_accuracy and es_accuracy > xgb_accuracy and es_accuracy > prophet_accuracy and es_accuracy >= ensemble_accuracy :
    print(f'Exponential Smoothing has the highest accuracy: {es_accuracy:.2f}%')
    print(es_pred)
    best_model = es_model
    best_accuracy = es_accuracy
    best_prediction = es_pred
    best_model_name = 'Exponential Smoothing'
  elif sarima_accuracy > es_accuracy and sarima_accuracy > xgb_accuracy and sarima_accuracy > prophet_accuracy and sarima_accuracy >= ensemble_accuracy:
    print(f'SARIMA has the highest accuracy: {sarima_accuracy:.2f}%')
    print(sarima_pred)
    best_model = sarima_results
    best_accuracy = sarima_accuracy
    best_prediction = sarima_pred
    best_model_name = 'SARIMA'
  elif xgb_accuracy > es_accuracy and xgb_accuracy > sarima_accuracy and xgb_accuracy > prophet_accuracy and xgb_accuracy >= ensemble_accuracy:
    print(f'XGBoost has the highest accuracy: {xgb_accuracy:.2f}%')
    print(xgb_pred)
    best_model = xgb_model
    best_accuracy = xgb_accuracy
    best_prediction = xgb_pred
    best_model_name = 'XGBoost'
  elif prophet_accuracy > es_accuracy and prophet_accuracy > sarima_accuracy and prophet_accuracy > xgb_accuracy and prophet_accuracy >= ensemble_accuracy:
    print(f'Prophet has the highest accuracy: {prophet_accuracy:.2f}%')
    print(prophet_pred)
    best_model = prophet_model
    best_accuracy = prophet_accuracy
    best_prediction = prophet_pred
    best_model_name = 'Prophet'
  elif ensemble_accuracy > es_accuracy and ensemble_accuracy > sarima_accuracy and ensemble_accuracy > xgb_accuracy and ensemble_accuracy > prophet_accuracy:
    print(f'Ensemble Model has the highest accuracy: {ensemble_accuracy:.2f}%')
    print(ensemble_pred)
    best_model = ensemble_pred
    best_accuracy = ensemble_accuracy
    best_prediction = ensemble_pred
    best_model_name = 'ensemble'
  else:
    print('The accuracy is lower than50% please provide more data')
  # Plot the results
  # plt.figure(figsize=(10, 6))
  # plt.plot(test_data[date_column], test_data[target_column], label='Actual')
  # plt.plot(test_data[date_column], ensemble_pred, label='Ensemble Prediction')
  # plt.plot(test_data[date_column], xgb_pred, label='XGBoost')
  # plt.plot(test_data[date_column], sarima_pred, label='SARIMA')
  # plt.plot(test_data[date_column], es_pred, label='Exponential Smoothing')
  # plt.plot(test_data[date_column], prophet_pred, label='Prophet')
  # plt.xlabel('Date')
  # plt.ylabel('Sales')
  # plt.title('Prediction vs Actual Sales')
  # plt.legend()
  # plt.xticks(rotation=45)
  # plt.show()

  return best_model,best_accuracy,best_prediction,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy,best_model_name

def best_model_training(data,best_model_name,feature_pred,prediction_data,feature_columns,date_column,target_column,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy):
      if best_model_name == 'Exponential Smoothing':
        best_mse = float("inf")
        best_model = None
        for trend in ['add', 'mul']:
            for seasonal in ['add', 'mul']:
                for seasonal_periods in [6, 12]:
                    esmodel = ExponentialSmoothing(data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()
                    es_pred = esmodel.forecast(3)
        return es_pred
        print (f'exponential smoothing output{es_pred}')
        joblib.dump(esmodel, bestmodel)
        print(f"Best model '{esmodel}' saved as {bestmodel}")

      elif best_model_name == 'SARIMA':
        p = d = q = range(0, 2)
        pdq = list(itertools.product(p, d, q))
        seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]
        param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}
        sarima_model = SARIMAX(data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})
        sarima_results = sarima_model.fit()
        sarima_pred = sarima_results.forecast(3)
        return sarima_pred
        print (f'sarima output{sarima_pred}')
        joblib.dump(sarima_model, bestmodel)
        print(f"Best model '{sarima_model}' saved as {bestmodel}")

      elif best_model_name == 'XGBoost':
          param_grid = {
          'n_estimators': [100, 200, 300],
          'learning_rate': [0.1, 0.2, 0.3],
            }
          xgb_model = XGBRegressor()
          grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')
          grid_search.fit(data[feature_columns], data[[target_column]])
          xgb_model = grid_search.best_estimator_
          xgb_pred = xgb_model.predict(feature_pred)
          return xgb_pred
          print (f'xgb output{xgb_pred}')
          joblib.dump(xgb_model, bestmodel)
          print(f"Best model '{xgb_model}' saved as {bestmodel}")

      elif best_model_name == 'Prophet':
        # prophet_data = data[[date_column, target_column]+feature_columns].rename(columns={date_column: 'ds', target_column: 'y'})
        # prophet_data.append(feature_pred)
        # prophet_data = pd.concat([prophet_data, feature_pred], axis=0)
        prophet_model = Prophet()
        for regressor in feature_columns:
            prophet_model.add_regressor(regressor)
        prophet_model.fit(data)
        # Predict future dates
        future = prediction_data[['ds']].copy()#prediction for 2 months
        for regressor in feature_columns:
            future[regressor] = feature_pred[regressor]  # Align feature values
        forecast = prophet_model.predict(future)
        prophet_pred = forecast['yhat'][-3:]
        return prophet_pred
        print (f'prophet output{prophet_pred}')
        joblib.dump(prophet_model, bestmodel)
        print(f"Best model '{prophet_model}' saved as {bestmodel}")

      else:
        best_mse = float("inf")
        best_model = None
        for trend in ['add', 'mul']:
            for seasonal in ['add', 'mul']:
                for seasonal_periods in [6, 12]:
                    model = ExponentialSmoothing(data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()
                    es_pred = model.forecast(3)

        p = d = q = range(0, 2)
        pdq = list(itertools.product(p, d, q))
        seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]
        param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}
        sarima_model = SARIMAX(data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})
        sarima_results = sarima_model.fit()
        sarima_pred = sarima_results.forecast(3)

        param_grid = {
            'n_estimators': [100, 200, 300],
            'learning_rate': [0.1, 0.2, 0.3],
        }
        xgb_model = XGBRegressor()
        grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')
        grid_search.fit(data[feature_columns], data[[target_column]])
        xgb_model = grid_search.best_estimator_
        xgb_pred = xgb_model.predict(feature_pred)

        prophet_model = Prophet()
        for regressor in feature_columns:
            prophet_model.add_regressor(regressor)
        prophet_model.fit(data)
        # Predict future dates
        future = prediction_data[['ds']].copy()#prediction for 2 months
        for regressor in feature_columns:
            future[regressor] = feature_pred[regressor]  # Align feature values
        forecast = prophet_model.predict(future)
        prophet_pred = forecast['yhat'][-3:]
        return prophet_pred
        print (f'prophet output{prophet_pred}')



        predictions = []
        if es_accuracy > 50:
            predictions.append(es_pred)
        if sarima_accuracy > 50:
            predictions.append(sarima_pred)
        if xgb_accuracy > 50:
            predictions.append(xgb_pred)
        if prophet_accuracy > 50:
            predictions.append(prophet_pred)
        # Calculate ensemble prediction
        if predictions:
            ensemble_pred = ensemble_predict(np.array(predictions))
            return ensemble_pred
        if predictions and 'ensemble_pred' in locals():
          ensemble_filename = "ensemble_model.pkl"
          joblib.dump(ensemble_pred, ensemble_filename)
          print(f"Ensemble model saved as {ensemble_filename}")
        else:
            print("No models with accuracy greater than 50% to create an ensemble.")
