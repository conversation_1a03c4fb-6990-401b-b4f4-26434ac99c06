{% extends "base.html" %}

{% block title %}Sales Forecast - Prediction Result{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h2 class="mb-0">Prediction Result</h2>
            </div>
            <div class="card-body">
                <div class="alert alert-info mb-4">
                    <h5>Model Used: {{ model_name }}</h5>
                </div>

                <div class="card mb-4">
                    <div class="card-body text-center">
                        <h5 class="card-title">Sales Prediction Result</h5>
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h6 class="card-subtitle mb-2 text-muted">Prediction Date</h6>
                                        <p class="card-text fs-4">{{ result.prediction_date }}</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card h-100 bg-light">
                                    <div class="card-body">
                                        <h6 class="card-subtitle mb-2 text-muted">Predicted Sales</h6>
                                        <p class="card-text prediction-value">{{ result.predicted_sales|round(2) }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="d-flex justify-content-between">
                    <a href="{{ url_for('predict') }}" class="btn btn-primary">Make Another Prediction</a>
                    <a href="{{ url_for('home') }}" class="btn btn-secondary">Back to Home</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
