/* Custom styles for Sales Forecast application */

.card {
    border-radius: 10px;
    transition: transform 0.3s;
}

.card:hover {
    transform: translateY(-5px);
}

.btn {
    border-radius: 5px;
    padding: 8px 20px;
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

.alert {
    border-radius: 8px;
    margin-bottom: 20px;
    padding: 15px;
    border-left: 5px solid;
    position: relative;
    z-index: 1000;
}

.alert-info {
    border-left-color: #0dcaf0;
    background-color: rgba(13, 202, 240, 0.1);
}

.alert-success {
    border-left-color: #198754;
    background-color: rgba(25, 135, 84, 0.1);
}

.alert-warning {
    border-left-color: #ffc107;
    background-color: rgba(255, 193, 7, 0.1);
}

.alert-danger {
    border-left-color: #dc3545;
    background-color: rgba(220, 53, 69, 0.1);
}

.alert i {
    font-size: 1.5rem;
    margin-right: 10px;
}

.alert-info i {
    color: #0dcaf0;
}

.alert-success i {
    color: #198754;
}

.alert-warning i {
    color: #ffc107;
}

.alert-danger i {
    color: #dc3545;
}

.prediction-value {
    font-size: 2rem;
    font-weight: bold;
    color: #28a745;
}

.feature-input {
    margin-bottom: 15px;
}

.upload-container {
    border: 2px dashed #ccc;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
    border-radius: 5px;
}

.upload-container:hover {
    border-color: #007bff;
}

.documentation-section {
    margin-bottom: 30px;
}

.api-example {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    font-family: monospace;
}
