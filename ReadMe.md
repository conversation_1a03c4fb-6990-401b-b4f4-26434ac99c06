
## Project Overview
This project is a web-based sales forecasting tool. Users can upload their sales datasets, train forecasting models, and make future sales predictions through a web interface or API. The backend is built with Flask, and the forecasting logic is modularized in the sales_forecast package.

# Main Components
Flask App (app.py): Handles web routes, file uploads, API endpoints, and user interaction.
Model Pipeline (pipeline): Contains scripts for data cleaning, feature identification, model training, and forecasting.
Templates & Static Files: HTML templates for the web interface and static assets (CSS, JS).
Models Directory: Stores trained models and configuration files.
# Workflow
A. User Uploads Dataset
User visits the /upload page and uploads a CSV file.
The app checks if the file is a valid CSV and has at least 10 rows.
The dataset is cleaned (clean_data) and key columns are identified (data_identifying).
The data is split into training and test sets.
B. Model Training
The model_training function is called with the training and test data.
Multiple models are trained and evaluated (the best model is selected based on performance).
The best model, its name, and configuration (feature columns, target column, date column) are saved to the models directory.
C. Prediction
User can go to the /predict page and enter feature values for a future date.
The app checks that all required features are provided.
The input is formatted and passed to the predict_future function, which uses the best trained model to make a prediction.
The predicted sales value is displayed to the user.
D. API Endpoints
/api/upload: Accepts a CSV file via POST, trains the model, and returns a JSON response.
/api/predict: Accepts a JSON payload with feature values and a date, returns the predicted sales as JSON.
# File/Directory Roles
app.py: Main Flask application, routes, and API logic.
data_preparation.py: Data cleaning and Dynamic column identification.
model_training.py: Model training and selection.
forecast.py: Prediction logic.
sales_forecast/templates/: HTML templates for the web interface.
sales_forecast/static/: Static files (CSS, JS).
models/: Stores the trained model (best_model.pkl), model name, and config as JSON.
# How the User Interacts
Upload Data: User uploads a CSV file with sales data.
Model Training: The app processes the data, trains models, and selects the best one.
Prediction: User enters new data (features + date) to get a sales forecast.
API Access: Developers can automate training and prediction via REST API endpoints.
# Error Handling
The app provides user-friendly error messages for missing files, invalid data, missing features, and prediction errors.
All errors are flashed on the web interface or returned as JSON in the API.
# Deployment
The app can be run locally with python app.py.
All dependencies should be listed in requirements.txt.
In summary:
This project provides an end-to-end solution for sales forecasting, from data upload and model training to making predictions, all accessible via a web interface or API. The modular design allows for easy maintenance and extension.

