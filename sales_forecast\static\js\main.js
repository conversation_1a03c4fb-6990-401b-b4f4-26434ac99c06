// Main JavaScript file for Sales Forecast application

document.addEventListener('DOMContentLoaded', function() {
    // Handle flash messages
    const alerts = document.querySelectorAll('.alert');

    // Make alerts more noticeable
    alerts.forEach(function(alert) {
        // Add animation to make it more noticeable
        alert.classList.add('animate__animated', 'animate__fadeIn');

        // Add click event to manually dismiss
        const closeBtn = alert.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', function() {
                const bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }
    });

    // Auto-dismiss alerts after 15 seconds (much longer)
    if (alerts.length > 0) {
        setTimeout(function() {
            alerts.forEach(function(alert) {
                // Add fade out animation
                alert.classList.remove('animate__fadeIn');
                alert.classList.add('animate__fadeOut');

                // Then close after animation completes
                setTimeout(function() {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 1000);
            });
        }, 15000); // 15 seconds
    }

    // Set today's date as default for date inputs
    const dateInputs = document.querySelectorAll('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];
    dateInputs.forEach(function(input) {
        if (!input.value) {
            input.value = today;
        }
    });

    // File upload preview
    const fileInput = document.getElementById('file');
    if (fileInput) {
        fileInput.addEventListener('change', function() {
            const fileNameDisplay = document.getElementById('file-name');
            if (fileNameDisplay) {
                if (this.files.length > 0) {
                    fileNameDisplay.textContent = this.files[0].name;
                    fileNameDisplay.classList.remove('text-muted');
                    fileNameDisplay.classList.add('text-primary');
                } else {
                    fileNameDisplay.textContent = 'No file selected';
                    fileNameDisplay.classList.remove('text-primary');
                    fileNameDisplay.classList.add('text-muted');
                }
            }
        });
    }

    // Form validation
    const forms = document.querySelectorAll('.needs-validation');
    Array.from(forms).forEach(function(form) {
        form.addEventListener('submit', function(event) {
            if (!form.checkValidity()) {
                event.preventDefault();
                event.stopPropagation();
            }
            form.classList.add('was-validated');
        }, false);
    });
});
