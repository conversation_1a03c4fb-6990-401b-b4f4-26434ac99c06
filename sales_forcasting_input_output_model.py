# -*- coding: utf-8 -*-
"""SALES_FORCASTING_INPUT_OUTPUT_MODEL.ipynb

Automatically generated by <PERSON><PERSON>.

Original file is located at
    https://colab.research.google.com/drive/1I7r7BUBxpdSZuvPX8JLP0eETlsPhhRIH
"""

def environment():
  !pip install statsmodels # install the statsmodels library
  !pip install itertools
  !pip install xgboost
  !pip install prophet

import pandas as pd
  import numpy as np
  import matplotlib.pyplot as plt
  from statsmodels.tsa.holtwinters import ExponentialSmoothing
  from statsmodels.tsa.statespace.sarimax import SARIMAX
  #from xgboost import XGBRegressor
  from typing_extensions import dataclass_transform
  import itertools
  from sklearn.ensemble import RandomForestRegressor
  from xgboost import XGBRegressor
  from sklearn.metrics import mean_squared_error
  from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
  from prophet import Prophet
  from math import e
  from google.colab import drive

def start():
  drive.mount('/content/drive')
  # Voting ensemble helper function

start()

# Voting ensemble helper function
def ensemble_predict(predictions):
    return np.mean(predictions, axis=0)

# data = pd.read_csv("/content/drive/MyDrive/Colab Notebooks/sales forcast/ATLAS sales forcasting/PF0033301_data.csv")
data = pd.read_csv('/content/drive/MyDrive/Colab Notebooks/sales forcast/ATLAS sales forcasting/PF0183701_data.csv')



def clean_data(data):
  data.info()
  data['ds'] = pd.to_datetime(data['ds'])
  data['ds'].tail()
  y=data['y'].name
  return data,y

# data.info()

# data['ds'] = pd.to_datetime(data['ds'])

# data['ds'].tail()

# y=data['y'].name

prediction_data = pd.DataFrame({
    'ds': ['2024-04-01', '2024-05-01','2024-06-01'],
    'MRP': [280,280,280],
    'Discount value': [856480, 252650,856480]
})

prediction_data['ds']=pd.to_datetime(prediction_data['ds'])

data.info()

def data_identifying(data,y):
  data.columns = data.columns.str.strip()
  # Identify the date column
  date_column = data.select_dtypes(include=['datetime', 'datetime64']).columns[0]
  # Assuming there's only one date column

  # Identify the numerical feature columns
  feature_columns = data.select_dtypes(include=['float64', 'int64']).columns.tolist()

  characteristic_columns = data.select_dtypes(include=['object']).columns.tolist()
  if characteristic_columns!=[]:
    print(f'Error: {characteristic_columns} will not consider as they are not numerical')

  # Identify the target column
  target_column = y

  # Remove the target column from the feature list if present
  if target_column in feature_columns:
      feature_columns.remove(target_column)

  print("Date column:", date_column)
  print("Feature columns:", feature_columns)
  print("Target column:", target_column)

  plt.figure(figsize=(16, 6))
  plt.plot(data['ds'], data['y'], label='Train Data')
  plt.xlabel('Date')
  plt.ylabel('Sales')
  plt.title('Data overview')
  plt.legend()

  plt.xticks(rotation=45)
  plt.show()

  return date_column,feature_columns,target_column

def split_data(data):
  #assuming prediction is for 2 data points
  train_data = data[[date_column]+feature_columns+[target_column]][:-3]
  test_data = data[[date_column]+feature_columns+[target_column]][-3:]
  return train_data,test_data

def model_training(train_data,test_data,data,date_column,target_column,feature_columns):
  # Exponential Smoothing with Hyperparameter Tuning
  best_mse = float("inf")
  best_model = None
  for trend in ['add', 'mul']:
      for seasonal in ['add', 'mul']:
          for seasonal_periods in [6, 12]:
              model = ExponentialSmoothing(train_data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()
              pred = model.forecast(len(test_data))
              mse = mean_squared_error(test_data[target_column], pred)
              if mse < best_mse:
                  best_mse = mse
                  best_model = model

  es_model = best_model
  es_pred = es_model.forecast(len(test_data))
  # SARIMA with Hyperparameter Tuning
  p = d = q = range(0, 2)
  pdq = list(itertools.product(p, d, q))
  seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]
  param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}
  sarima_model = SARIMAX(train_data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})
  sarima_results = sarima_model.fit()
  sarima_pred = sarima_results.forecast(len(test_data))

  # XGBoost with Hyperparameter Tuning
  param_grid = {
      'n_estimators': [100, 200, 300],
      'learning_rate': [0.1, 0.2, 0.3],
  }
  xgb_model = XGBRegressor()
  grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')
  grid_search.fit(train_data[feature_columns], train_data[[target_column]])
  xgb_model = grid_search.best_estimator_
  xgb_pred = xgb_model.predict(test_data[feature_columns])

  # Prophet model with regressors

  # Prepare the data for Prophet
  prophet_data = train_data[[date_column, target_column]].rename(columns={date_column: 'ds', target_column: 'y'})

  prophet_model = Prophet()
  for regressor in feature_columns:
      prophet_model.add_regressor(regressor)
  prophet_model.fit(train_data)

  # Predict future dates
  future = test_data[['ds']].copy()  #prediction for 2 months
  for regressor in feature_columns:
      future[regressor] = test_data[regressor]  # Align feature values

  forecast = prophet_model.predict(future)
  prophet_pred = forecast['yhat'][-len(test_data):]

    # Calculate RMSE for each model
  es_rmse = np.sqrt(mean_squared_error(test_data[target_column], es_pred))
  sarima_rmse = np.sqrt(mean_squared_error(test_data[target_column], sarima_pred))
  xgb_rmse = np.sqrt(mean_squared_error(test_data[target_column], xgb_pred))
  prophet_rmse = np.sqrt(mean_squared_error(test_data[target_column], prophet_pred))

   # Calculate accuracy for each model
  es_accuracy = 100 - (es_rmse / test_data[target_column].mean()) * 100
  sarima_accuracy = 100 - (sarima_rmse / test_data[target_column].mean()) * 100
  xgb_accuracy = 100 - (xgb_rmse / test_data[target_column].mean()) * 100
  prophet_accuracy = 100 - (prophet_rmse / test_data[target_column].mean()) * 100

  # Print the results
  print(f'Exponential Smoothing RMSE: {es_rmse:.2f}')
  print(f'Exponential Smoothing Accuracy: {es_accuracy:.2f}%')

  print(f'SARIMA RMSE: {sarima_rmse:.2f}')
  print(f'SARIMA Accuracy: {sarima_accuracy:.2f}%')

  print(f'XGBoost RMSE: {xgb_rmse:.2f}')
  print(f'XGBoost Accuracy: {xgb_accuracy:.2f}%')


  print(f'Prophet RMSE: {prophet_rmse:.2f}')
  print(f'Prophet Accuracy: {prophet_accuracy:.2f}%')

  # Combine predictions from models with accuracy > 50%
  predictions = []
  if es_accuracy > 50:
    predictions.append(es_pred)
  if sarima_accuracy > 50:
    predictions.append(sarima_pred)
  if xgb_accuracy > 50:
    predictions.append(xgb_pred)
  if prophet_accuracy > 50:
    predictions.append(prophet_pred)
  # Calculate ensemble prediction
  if predictions:
    ensemble_pred = ensemble_predict(np.array(predictions))
    ensemble_rmse = np.sqrt(mean_squared_error(test_data[target_column], ensemble_pred))
    ensemble_accuracy = 100 - (np.mean(np.abs((test_data[target_column] - ensemble_pred) / test_data[target_column])) * 100)
    print(f'Ensemble RMSE: {ensemble_rmse:.2f}')
    print(f'Ensemble Accuracy: {ensemble_accuracy:.2f}%')
  else:
    print("No models with accuracy greater than 50% to create an ensemble.")

  # Calculate ensemble prediction
  if predictions:
    ensemble_pred = ensemble_predict(np.array(predictions))
    #check the best accurate model
  if es_accuracy > sarima_accuracy and es_accuracy > xgb_accuracy and es_accuracy > prophet_accuracy and es_accuracy >= ensemble_accuracy :
    print(f'Exponential Smoothing has the highest accuracy: {es_accuracy:.2f}%')
    print(es_pred)
    best_model = es_model
    best_accuracy = es_accuracy
    best_prediction = es_pred
    best_model_name = 'Exponential Smoothing'
  elif sarima_accuracy > es_accuracy and sarima_accuracy > xgb_accuracy and sarima_accuracy > prophet_accuracy and sarima_accuracy >= ensemble_accuracy:
    print(f'SARIMA has the highest accuracy: {sarima_accuracy:.2f}%')
    print(sarima_pred)
    best_model = sarima_results
    best_accuracy = sarima_accuracy
    best_prediction = sarima_pred
    best_model_name = 'SARIMA'
  elif xgb_accuracy > es_accuracy and xgb_accuracy > sarima_accuracy and xgb_accuracy > prophet_accuracy and xgb_accuracy >= ensemble_accuracy:
    print(f'XGBoost has the highest accuracy: {xgb_accuracy:.2f}%')
    print(xgb_pred)
    best_model = xgb_model
    best_accuracy = xgb_accuracy
    best_prediction = xgb_pred
    best_model_name = 'XGBoost'
  elif prophet_accuracy > es_accuracy and prophet_accuracy > sarima_accuracy and prophet_accuracy > xgb_accuracy and prophet_accuracy >= ensemble_accuracy:
    print(f'Prophet has the highest accuracy: {prophet_accuracy:.2f}%')
    print(prophet_pred)
    best_model = prophet_model
    best_accuracy = prophet_accuracy
    best_prediction = prophet_pred
    best_model_name = 'Prophet'
  elif ensemble_accuracy > es_accuracy and ensemble_accuracy > sarima_accuracy and ensemble_accuracy > xgb_accuracy and ensemble_accuracy > prophet_accuracy:
    print(f'Ensemble Model has the highest accuracy: {ensemble_accuracy:.2f}%')
    print(ensemble_pred)
    best_model = ensemble_pred
    best_accuracy = ensemble_accuracy
    best_prediction = ensemble_pred
    best_model_name = 'ensemble'
  else:
    print('The accuracy is lower than50% please provide more data')
  # Plot the results
  plt.figure(figsize=(10, 6))
  plt.plot(test_data[date_column], test_data[target_column], label='Actual')
  plt.plot(test_data[date_column], ensemble_pred, label='Ensemble Prediction')
  plt.plot(test_data[date_column], xgb_pred, label='XGBoost')
  plt.plot(test_data[date_column], sarima_pred, label='SARIMA')
  plt.plot(test_data[date_column], es_pred, label='Exponential Smoothing')
  plt.plot(test_data[date_column], prophet_pred, label='Prophet')
  plt.xlabel('Date')
  plt.ylabel('Sales')
  plt.title('Prediction vs Actual Sales')
  plt.legend()
  plt.xticks(rotation=45)
  plt.show()

  return best_model,best_accuracy,best_prediction,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy,best_model_name

# def best_model_training(data,best_model_name,feature_columns_pred,date_column_pred,feature_columns,date_column,target_column,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy):
#    # Exponential Smoothing with Hyperparameter Tuning
#     if best_model_name == 'Exponential Smoothing':
#       best_mse = float("inf")
#       best_model = None
#       for trend in ['add', 'mul']:
#           for seasonal in ['add', 'mul']:
#               for seasonal_periods in [6, 12]:
#                   model = ExponentialSmoothing(data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()
#                   es_pred = model.forecast(2)
#                   return es_pred

#     elif best_model_name == 'SARIMA':
#       p = d = q = range(0, 2)
#       pdq = list(itertools.product(p, d, q))
#       seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]
#       param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}
#       sarima_model = SARIMAX(data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})
#       sarima_results = sarima_model.fit()
#       sarima_pred = sarima_results.forecast(2)
#       return sarima_pred

#     elif best_model_name == 'Xgboost':
#       param_grid = {
#           'n_estimators': [100, 200, 300],
#           'learning_rate': [0.1, 0.2, 0.3],
#       }
#       xgb_model = XGBRegressor()
#       grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')
#       grid_search.fit(data[feature_columns], data[[target_column]])
#       xgb_model = grid_search.best_estimator_
#       xgb_pred = xgb_model.predict(feature_columns_pred)
#       return xgb_pred

#     elif best_model_name == 'Prophet':
#       #Prepare the data for Prophet
#       prophet_data = data[[date_column, target_column]].rename(columns={date_column: 'ds', target_column: 'y'})
#       prophet_model = Prophet()
#       for regressor in feature_columns:
#           prophet_model.add_regressor(regressor)
#       prophet_model.fit(data)
#       # Predict future dates
#       future = test_data[['ds']].copy() #prediction for 2 months
#       for regressor in feature_columns:
#           future[regressor] = test_data[regressor]  # Align feature values

#       forecast = prophet_model.predict(future)
#       prophet_pred = forecast['yhat'][-2:]
#       return prophet_pred
#     else:
#     # Calculate RMSE for each model

#   # Print the results
#   # Combine predictions from models with accuracy > 50%
#       predictions = []
#       if es_accuracy > 50:
#         predictions.append(es_pred)
#       if sarima_accuracy > 50:
#         predictions.append(sarima_pred)
#       if xgb_accuracy > 50:
#         predictions.append(xgb_pred)
#       if prophet_accuracy > 50:
#         predictions.append(prophet_pred)
#       # Calculate ensemble prediction
#       if predictions:
#         ensemble_pred = ensemble_predict(np.array(predictions))
#         ensemble_rmse = np.sqrt(mean_squared_error(test_data[target_column], ensemble_pred))
#         ensemble_accuracy = 100 - (np.mean(np.abs((test_data[target_column] - ensemble_pred) / test_data[target_column])) * 100)
#         print(f'Ensemble RMSE: {ensemble_rmse:.2f}')
#         print(f'Ensemble Accuracy: {ensemble_accuracy:.2f}%')
#       else:
#         print("No models with accuracy greater than 50% to create an ensemble.")

#       # Calculate ensemble prediction
#       if predictions:
#         ensemble_pred = ensemble_predict(np.array(predictions))
      ###########################################################

def best_model_training(data,best_model_name,feature_pred,prediction_data,feature_columns,date_column,target_column,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy):
      if best_model_name == 'Exponential Smoothing':
        best_mse = float("inf")
        best_model = None
        for trend in ['add', 'mul']:
            for seasonal in ['add', 'mul']:
                for seasonal_periods in [6, 12]:
                    model = ExponentialSmoothing(data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()
                    es_pred = model.forecast(3)
        return es_pred
        print (f'exponential smoothing output{es_pred}')

      elif best_model_name == 'SARIMA':
        p = d = q = range(0, 2)
        pdq = list(itertools.product(p, d, q))
        seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]
        param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}
        sarima_model = SARIMAX(data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})
        sarima_results = sarima_model.fit()
        sarima_pred = sarima_results.forecast(3)
        return sarima_pred
        print (f'sarima output{sarima_pred}')

      elif best_model_name == 'XGBoost':
          param_grid = {
          'n_estimators': [100, 200, 300],
          'learning_rate': [0.1, 0.2, 0.3],
            }
          xgb_model = XGBRegressor()
          grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')
          grid_search.fit(data[feature_columns], data[[target_column]])
          xgb_model = grid_search.best_estimator_
          xgb_pred = xgb_model.predict(feature_pred)
          return xgb_pred
          print (f'xgb output{xgb_pred}')

      elif best_model_name == 'Prophet':
        # prophet_data = data[[date_column, target_column]+feature_columns].rename(columns={date_column: 'ds', target_column: 'y'})
        # prophet_data.append(feature_pred)
        # prophet_data = pd.concat([prophet_data, feature_pred], axis=0)
        prophet_model = Prophet()
        for regressor in feature_columns:
            prophet_model.add_regressor(regressor)
        prophet_model.fit(data)
        # Predict future dates
        future = prediction_data[['ds']].copy()#prediction for 2 months
        for regressor in feature_columns:
            future[regressor] = feature_pred[regressor]  # Align feature values
        forecast = prophet_model.predict(future)
        prophet_pred = forecast['yhat'][-3:]
        return prophet_pred
        print (f'prophet output{prophet_pred}')

      else:
        best_mse = float("inf")
        best_model = None
        for trend in ['add', 'mul']:
            for seasonal in ['add', 'mul']:
                for seasonal_periods in [6, 12]:
                    model = ExponentialSmoothing(data[target_column], trend=trend, seasonal=seasonal, seasonal_periods=seasonal_periods).fit()
                    es_pred = model.forecast(3)

        p = d = q = range(0, 2)
        pdq = list(itertools.product(p, d, q))
        seasonal_pdq = [(x[0], x[1], x[2], 12) for x in list(itertools.product(p, d, q))]
        param_grid = {'order': pdq, 'seasonal_order': seasonal_pdq}
        sarima_model = SARIMAX(data[target_column], **{'order': (2, 1, 2), 'seasonal_order': (1, 0, 1, 12)})
        sarima_results = sarima_model.fit()
        sarima_pred = sarima_results.forecast(3)

        param_grid = {
            'n_estimators': [100, 200, 300],
            'learning_rate': [0.1, 0.2, 0.3],
        }
        xgb_model = XGBRegressor()
        grid_search = GridSearchCV(xgb_model, param_grid, cv=TimeSeriesSplit(n_splits=3), scoring='neg_mean_squared_error')
        grid_search.fit(data[feature_columns], data[[target_column]])
        xgb_model = grid_search.best_estimator_
        xgb_pred = xgb_model.predict(feature_pred)

        prophet_model = Prophet()
        for regressor in feature_columns:
            prophet_model.add_regressor(regressor)
        prophet_model.fit(data)
        # Predict future dates
        future = prediction_data[['ds']].copy()#prediction for 2 months
        for regressor in feature_columns:
            future[regressor] = feature_pred[regressor]  # Align feature values
        forecast = prophet_model.predict(future)
        prophet_pred = forecast['yhat'][-3:]
        return prophet_pred
        print (f'prophet output{prophet_pred}')



        predictions = []
        if es_accuracy > 50:
            predictions.append(es_pred)
        if sarima_accuracy > 50:
            predictions.append(sarima_pred)
        if xgb_accuracy > 50:
            predictions.append(xgb_pred)
        if prophet_accuracy > 50:
            predictions.append(prophet_pred)
        # Calculate ensemble prediction
        if predictions:
            ensemble_pred = ensemble_predict(np.array(predictions))
            return ensemble_pred
            # ensemble_rmse = np.sqrt(mean_squared_error(test_data[target_column], ensemble_pred))
            # ensemble_accuracy = 100 - (np.mean(np.abs((test_data[target_column] - ensemble_pred) / test_data[target_column])) * 100)
            # print(f'Ensemble RMSE: {ensemble_rmse:.2f}')
            # print(f'Ensemble Accuracy: {ensemble_accuracy:.2f}%')
        else:
            print("No models with accuracy greater than 50% to create an ensemble.")

data.head()

def future_prediction(prediction_data,data,date_column,feature_columns,target_column,best_model_name,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy):
    feature_columns_pred = prediction_data.select_dtypes(include=['float64', 'int64']).columns.tolist()
    date_column_pred = prediction_data.select_dtypes(include=['datetime', 'datetime64']).columns[0]
    prediction_data[date_column_pred].rename('ds', inplace=True)
    # prediction_data[target_column].rename('y', inplace=True)
    data = data[[date_column, target_column]+feature_columns].rename(columns={date_column: 'ds', target_column: 'y'})
    # print(data.head())
    # print(prediction_data.head())
    # print(feature_columns_pred)
  # Validate the columns before proceeding to prediction
    try:
        check_column_alignment(feature_columns, feature_columns_pred)
    except ValueError as e:
        print(e)
    else:
      date_pred=prediction_data[date_column_pred]
      feature_pred=prediction_data[feature_columns_pred]
      print(date_pred)
      print(feature_pred)

      pred=best_model_training(data,best_model_name,feature_pred,prediction_data,feature_columns,date_column,target_column,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy)

      # if best_model_name == 'Exponential Smoothing':
      #   future_pred = best_model.forecast(len(prediction_data)+2).tail(2)
      # elif best_model_name == 'SARIMA':
      #   future_pred = best_model.forecast(len(prediction_data)+2).tail(2)
      # elif best_model_name == 'XGBoost':
      #   # feature_columns_concat= pd.concat([test_data[feature_columns],feature_columns_pred])
      #   # future_pred = best_model.predict(feature_columns_concat).tail(2)
      #   pred=best_model_training(data,best_model_name,feature_columns_pred,date_column_pred,feature_columns,date_column,target_column)
      #   # future_pred = best_model(data,best_model_name,feature_columns_pred,feature_columns,date_column,target_column)
      #   # future_pred = pd.Series(future_pred)
      # elif best_model_name == 'Prophet':
      #   feature_columns_concat=pd.concat([test_data[feature_columns],feature_columns_pred])
      #   future_pred = best_model.predict(feature_columns_concat).tail(2)
      # else :
      #   print("THERE WAS AN ERROR FIX IT HERE ENSEMBLE MODEL SHOULD RUN")

      return pred

def check_column_alignment(train_columns, prediction_columns):
      missing_columns = set(train_columns) - set(prediction_columns)
      extra_columns = set(prediction_columns) - set(train_columns)
      error_message = ""
      if missing_columns or extra_columns:
          # error_message = ""
          if missing_columns:
              error_message += f"Missing columns in prediction data: {missing_columns}\n"
          if extra_columns:
              error_message += f"Extra columns in prediction data: {extra_columns}\n"
      if error_message:
        raise ValueError(f"Column mismatch error:\n{error_message}")
      else:
       None

clean_data= clean_data(data)
data=clean_data[0]
y=clean_data[1]
cleaned_data=data_identifying(data,y)
date_column=cleaned_data[0]
feature_columns=cleaned_data[1]
target_column=cleaned_data[2]

data_splitted=split_data(data)
train_data=data_splitted[0]
test_data=data_splitted[1]

model_selected=model_training(train_data,test_data,data,date_column,target_column,feature_columns)
best_model=model_selected[0]
best_accuracy=model_selected[1]
best_prediction=model_selected[2]
xgb_accuracy=model_selected[3]
es_accuracy=model_selected[4]
sarima_accuracy=model_selected[5]
prophet_accuracy=model_selected[6]
best_model_name=model_selected[7]
# best_model_name='XGBoost'
prediction = future_prediction(prediction_data,data,date_column,feature_columns,target_column,best_model_name,xgb_accuracy,es_accuracy,sarima_accuracy,prophet_accuracy)
print(prediction)
# best_model=model_selected[0]
# best_accuracy=model_selected[1]
# best_prediction=model_selected[2]
# xgb_accuracy=model_selected[3]
# es_accuracy=model_selected[4]
# sarima_accuracy=model_selected[5]
# prophet_accuracy=model_selected[6]
# xgb_pred=model_selected[7]
# es_pred=model_selected[8]
# sarima_pred=model_selected[9]
# prophet_pred=model_selected[10]
# best_model_name=model_selected[11]


# print(f'best model{best_model_name}')
# print(best_accuracy)
# print(best_prediction)
# # print(f'ensemble prediction{prediction_ensemble}')
# if best_model_name != 'ensemble':
#   print(f'best prediction{best_prediction}')
#   # prediction=future_prediction(prediction_data,date_column,feature_columns,target_column,best_model,best_model_name)
#   prediction = best_model
#   print(f'finalprediction = {prediction}')
# else:
#     predictions = []
#     if es_accuracy > 50:
#        predictions.append(es_pred)
#     if sarima_accuracy > 50:
#       predictions.append(sarima_pred)
#     if xgb_accuracy > 50:
#       predictions.append(xgb_pred)
#     if prophet_accuracy > 50:
#       predictions.append(prophet_pred)
#       if predictions:
#           ensemble_pred = ensemble_predict(np.array(predictions))
#           prediction_ensemble = ensemble_pred
#           print(f'Ensemble prediction: {ensemble_pred}')
#     else:
#           print("No models with accuracy greater than 50% to create an ensemble.")