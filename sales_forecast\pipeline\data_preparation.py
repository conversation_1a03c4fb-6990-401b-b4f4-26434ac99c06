import pandas as pd
import numpy as np
def clean_data(data):
  """
  Clean and prepare the data for modeling or prediction

  Args:
      data: DataFrame containing the data

  Returns:
      Tuple of (cleaned_data, target_column_name)
  """
  # Print data info for debugging
  data.info()

  # Convert date column to datetime
  if 'ds' in data.columns:
    data['ds'] = pd.to_datetime(data['ds'])
    data['ds'].tail()

  # Get target column name if it exists
  if 'y' in data.columns:
    y = data['y'].name
  else:
    # For prediction data, there might not be a target column
    y = None

  return data, y

def data_identifying(data, y=None):
  """
  Identify date column, feature columns, and target column in the data

  Args:
      data: DataFrame containing the data
      y: Name of the target column (optional)

  Returns:
      Tuple of (date_column, feature_columns, target_column)
  """
  data.columns = data.columns.str.strip()

  # Identify the date column
  date_columns = data.select_dtypes(include=['datetime', 'datetime64']).columns
  if len(date_columns) > 0:
    date_column = date_columns[0]
  else:
    # If no datetime column is found, assume 'ds' is the date column
    date_column = 'ds'

  # Identify the numerical feature columns
  feature_columns = data.select_dtypes(include=['float64', 'int64']).columns.tolist()

  # Check for non-numeric columns
  characteristic_columns = data.select_dtypes(include=['object']).columns.tolist()
  if characteristic_columns and characteristic_columns != [date_column]:
    print(f'Warning: {characteristic_columns} will not be considered as they are not numerical')

  # Identify the target column
  target_column = y

  # Remove the target column from the feature list if present
  if target_column in feature_columns:
      feature_columns.remove(target_column)

  # For prediction, we don't need to return the target column
  if target_column is None and len(feature_columns) > 0:
    print(f"Using features: {feature_columns}")

  return date_column, feature_columns, target_column