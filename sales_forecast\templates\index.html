{% extends "base.html" %}

{% block title %}Sales Forecast - Home{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="mb-0">Sales Forecasting Application</h2>
            </div>
            <div class="card-body">
                <h5 class="card-title">Welcome to the Sales Forecasting Tool</h5>
                <p class="card-text">
                    This application helps you predict future sales based on historical data. 
                    Upload your sales dataset and get accurate forecasts using advanced machine learning models.
                </p>
                
                <div class="row mt-4">
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">Upload Dataset</h5>
                                <p class="card-text">Upload your CSV file containing historical sales data to train the forecasting model.</p>
                                <a href="{{ url_for('upload_dataset') }}" class="btn btn-primary">Upload Dataset</a>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card h-100">
                            <div class="card-body">
                                <h5 class="card-title">Make Prediction</h5>
                                <p class="card-text">Enter data points to predict future sales using the trained model.</p>
                                <a href="{{ url_for('predict') }}" class="btn btn-success">Make Prediction</a>
                            </div>
                        </div>
                    </div>
                </div>
                
                {% if model_name %}
                <div class="alert alert-success mt-4">
                    <h5>Current Model Information</h5>
                    <p>Currently using: <strong>{{ model_name }}</strong></p>
                </div>
                {% else %}
                <div class="alert alert-warning mt-4">
                    <h5>No Model Trained</h5>
                    <p>Please upload a dataset to train a model first.</p>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
