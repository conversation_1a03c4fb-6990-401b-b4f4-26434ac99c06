# import joblib

# feature_pred=input
# # Load the saved model from the joblib file
# model_path = "bestmodel.pkl"  # Replace with the actual path to your joblib file
# Best_model = joblib.load(model_path)
# # Replace with your feature data
# predictions = Best_model.predict(feature_pred)
# print(f"Predictions: {predictions}")
# # Example usage of the loaded model
# # Replace 'input_data' with the actual data you want to predict
# # input_data = [[value1, value2, value3, ...]]
# # prediction = loaded_model.predict(input_data)
# # print("Prediction:", prediction)

# 1. Import necessary libraries
import pandas as pd
import numpy as np
import joblib
import json
from xgboost import XGBRegressor
from statsmodels.tsa.statespace.sarimax import SARIMAX
from statsmodels.tsa.holtwinters import ExponentialSmoothing
from prophet import Prophet

# Initialize variables that will be set when the module is imported
date_column = None
feature_columns = None
target_column = None
best_model_name = None
best_model = None

# Try to load config if it exists
try:
    # 2. Load Config Files
    with open('models/config.json', 'r') as f:
        config = json.load(f)

    date_column = config['date_column']
    feature_columns = config['feature_columns']
    target_column = config['target_column']

    # Load Best Model Name
    with open('models/best_model_name.json', 'r') as f:
        model_info = json.load(f)
    best_model_name = model_info['best_model_name']

    # 3. Load Saved Best Model
    best_model = joblib.load('models/best_model.pkl')
    print(f"Loaded model: {best_model_name}")
except Exception as e:
    print(f"Note: No model loaded yet. {e}")

# Example prediction data format
# prediction_data = pd.DataFrame({
#     'ds': ['2024-07-01'],
#     'feature1': [value1],
#     'feature2': [value2]
# })
# prediction_data['ds'] = pd.to_datetime(prediction_data['ds'])

# 5. Forecasting Function
def predict_future(model, model_name, prediction_data, feature_cols=None):
    """
    Make predictions using the trained model

    Args:
        model: The trained model object
        model_name: String name of the model type
        prediction_data: DataFrame with feature values
        feature_cols: List of feature column names (optional)

    Returns:
        Predictions as a numpy array
    """
    import numpy as np
    import pandas as pd

    # Handle feature columns
    if feature_cols is None and feature_columns is not None:
        feature_cols = feature_columns
    elif feature_cols is None:
        feature_cols = prediction_data.columns.tolist()

    # Remove 'ds' from feature_cols if present (for feature-only models)
    features_only = [col for col in feature_cols if col != 'ds']

    # If there are no features, only allow models that do not require features
    if not features_only:
        if model_name in ['Exponential Smoothing', 'SARIMA', 'Prophet', 'ensemble']:
            if model_name == 'Exponential Smoothing':
                pred = model.forecast(len(prediction_data))
            elif model_name == 'SARIMA':
                pred = model.forecast(len(prediction_data))
            elif model_name == 'Prophet':
                try:
                    future = pd.DataFrame({'ds': pd.to_datetime(prediction_data['ds'])})
                    pred = model.predict(future)['yhat'].values
                except Exception as e:
                    print(f"Error in Prophet prediction: {e}")
                    pred = np.array([0])
            elif model_name == 'ensemble':
                pred = np.array([model] * len(prediction_data))
        else:
            raise ValueError(f"Model '{model_name}' is not suitable for datasets without features.")
        return pred

    # ...existing code for models with features...
    if model_name == 'Exponential Smoothing':
        pred = model.forecast(len(prediction_data))
    elif model_name == 'SARIMA':
        pred = model.forecast(len(prediction_data))
    elif model_name == 'XGBoost':
        pred = model.predict(prediction_data[features_only])
    elif model_name == 'Prophet':
        try:
            if not features_only:
                # Only use 'ds' column if no features
                future = pd.DataFrame({'ds': pd.to_datetime(prediction_data['ds'])})
            else:
                future = pd.DataFrame({'ds': pd.to_datetime(prediction_data['ds'])})
                for feature in features_only:
                    if feature in prediction_data.columns:
                        future[feature] = prediction_data[feature].values
            print(f"Prophet prediction: future DataFrame =\n{future}")
            pred = model.predict(future)['yhat'].values
        except Exception as e:
            print(f"Error in Prophet prediction: {e}\nFuture DataFrame:\n{future}\nPrediction data columns: {prediction_data.columns.tolist()}\nModel expects features: {features_only}")
            pred = np.full(len(prediction_data), np.nan)
    elif model_name == 'ensemble':
        pred = np.array([model] * len(prediction_data))
    else:
        raise ValueError(f"Unsupported model type: {model_name}")
    return pred

# Only run this code if the script is executed directly (not imported)
if __name__ == "__main__":
    # Example prediction data
    prediction_data = pd.DataFrame({
        'ds': ['2024-07-01', '2024-08-01', '2024-09-01'],
        'MRP': [290, 295, 300],
        'Discount value': [900000, 870000, 890000]
    })
    prediction_data['ds'] = pd.to_datetime(prediction_data['ds'])

    # Run prediction if model is loaded
    if best_model is not None and best_model_name is not None:
        # 6. Run Prediction
        forecast = predict_future(best_model, best_model_name, prediction_data)

        # 7. Show Forecast Results
        print("\n📈 Forecasted Sales:")
        for date, pred in zip(prediction_data['ds'], forecast):
            print(f"{date.date()} → {pred:.2f}")
    else:
        print("No model loaded. Please train a model first.")



