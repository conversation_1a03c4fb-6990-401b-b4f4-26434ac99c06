Dataset Requirements:
Time Frequency: The data should be organized in monthly intervals. This is important because the forecasting models (especially SARIMA and Prophet) are configured to work with monthly seasonality patterns.
Date Column: The dataset must have a date column named 'ds' that represents the time periods (months). This column will be automatically converted to datetime format by the application.
Target Column: The dataset should have a target column named 'y' that contains the sales values you want to predict.
Feature Columns: The dataset should include numerical feature columns that might influence sales (e.g., price, marketing spend, promotions, etc.).
Minimum Size: The dataset must have at least 10 rows (months) of data to train a model effectively.
Format: The data should be in CSV format.
Example Dataset Structure:
Here's an example of how your dataset might look:
ds,y,price,marketing_spend,promotion
2022-01-01,1200,100,5000,0
2022-02-01,1350,100,5500,1
2022-03-01,1400,105,6000,1
2022-04-01,1300,105,5000,0
2022-05-01,1450,110,5500,0
2022-06-01,1600,110,6000,1
2022-07-01,1550,115,6500,1
2022-08-01,1500,115,6000,0
2022-09-01,1650,120,6500,0
2022-10-01,1800,120,7000,1
2022-11-01,1900,125,7500,1
2022-12-01,2100,125,8000,1
In this example:

'ds' is the date column (first day of each month)
'y' is the target column (sales values)
'price', 'marketing_spend', and 'promotion' are feature columns that might influence sales
The application will use this historical data to train various forecasting models (Exponential Smoothing, SARIMA, XGBoost, and Prophet) and select the best-performing one for making future predictions.